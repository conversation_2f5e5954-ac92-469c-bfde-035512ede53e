# 🤖 AI Email Assistant - User Guide

## 🚀 How to Use Your Enhanced AI Email Assistant

### 📋 Setup (One-time)
1. **Install the Extension**: Load the extension in Chrome (Developer mode)
2. **Set API Key**: 
   - Click the extension icon or go to Chrome Extensions → AI Email Assistant → Options
   - Enter your Google Gemini API key
   - Click "Save Key"

### 💡 How to Use in Gmail

#### 🔍 Finding the AI Button
The **✨ AI Reply** button will automatically appear in:
- **Compose windows** (when writing new emails)
- **Reply areas** (when replying to emails)
- **Gmail toolbars** (next to other Gmail buttons)

#### 🎯 Four Powerful AI Features

**1. 📧 Smart Reply**
- **When to use**: Replying to received emails
- **What it does**: Analyzes the original email and generates a contextual, professional reply
- **How**: Click "✨ AI Reply" → "Smart Reply"
- **Best for**: Quick, intelligent responses to any email

**2. ✍️ Improve Draft**
- **When to use**: When you've written some content but want to make it better
- **What it does**: Takes your draft and makes it more professional, clear, and polished
- **How**: Write some content first → Click "✨ AI Reply" → "Improve Draft"
- **Best for**: Enhancing emails you've already started

**3. 📝 Summarize Email**
- **When to use**: When dealing with long email threads or complex emails
- **What it does**: Creates a concise summary with key points, action items, and deadlines
- **How**: Open an email → Click "✨ AI Reply" → "Summarize Email"
- **Best for**: Understanding long emails quickly

**4. 🎯 Quick Response**
- **When to use**: When you need a fast, professional template
- **What it does**: Provides pre-written professional responses you can customize
- **How**: Click "✨ AI Reply" → "Quick Response" → Choose template
- **Templates include**:
  - Thank you messages
  - Meeting requests
  - Polite declines
  - Follow-ups
  - Apologies
  - And more!

### 📖 Step-by-Step Examples

#### Example 1: Replying to a Business Email
1. Open the email you want to reply to
2. Click "Reply"
3. Look for the **✨ AI Reply** button in the toolbar
4. Click it and select **"Smart Reply"**
5. The AI will analyze the original email and write a professional response
6. Edit as needed and send!

#### Example 2: Improving Your Draft
1. Start composing an email
2. Write a rough draft (even just a few sentences)
3. Click **✨ AI Reply** → **"Improve Draft"**
4. Your draft will be enhanced and made more professional
5. Review and send!

#### Example 3: Quick Professional Response
1. Open a compose window
2. Click **✨ AI Reply** → **"Quick Response"**
3. Choose from templates like "Thank you", "Schedule meeting", etc.
4. The template will be inserted
5. Customize as needed and send!

### 🎨 Visual Features
- **Professional Design**: Modern gradient buttons with hover effects
- **Loading Indicators**: Shows "🧠 Generating..." while AI works
- **Smart Dropdown**: Clean menu with icons and descriptions
- **Status Messages**: Clear feedback on success/errors

### 🔧 Troubleshooting

**Button Not Appearing?**
- Refresh Gmail page
- Make sure you're in a compose window or reply area
- Check if extension is enabled

**"API Key Not Found" Error?**
- Go to extension options and set your Gemini API key
- Make sure the key is valid and active

**AI Not Working?**
- Check your internet connection
- Verify your Gemini API key is correct
- Make sure you have API quota remaining

**Poor AI Responses?**
- Provide more context in your draft
- Try the "Improve Draft" feature instead
- Use "Quick Response" for simple replies

### 💡 Pro Tips
1. **Combine Features**: Use "Quick Response" to start, then "Improve Draft" to polish
2. **Provide Context**: The more context you give, the better the AI responses
3. **Review Before Sending**: Always review AI-generated content before sending
4. **Use Summaries**: Great for understanding long email chains before responding
5. **Keyboard Shortcut**: Press Enter in the API key field to save quickly

### 🔒 Privacy & Security
- Your API key is stored locally in your browser
- Emails are sent to Google's Gemini API for processing
- No data is stored on our servers
- You can delete your API key anytime from the options page

### 🆘 Need Help?
- Check that your Gemini API key is valid
- Make sure you're on mail.google.com
- Try refreshing the page
- Ensure the extension has proper permissions

---

**Enjoy your enhanced email productivity! 🚀**
